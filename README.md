# Astro

```sh
npm create astro@latest -- --template basics
```

Borrar `node_modules/`, mover `typescript` a `devDependencies` y `npm i`.

Crear `./public/robots.txt` y `tsconfig.json`.

Configurar el dominio en `astro.config.mjs`, en mi caso `site: "https://bookea.link"`

Agregar que chequee TypeScript antes del build. Instalar `npm i @astrojs/check -D` y en `package.json`:

```json
"scripts": {
  "build": "astro check && astro build",
}
```

Agregar transiciones: https://docs.astro.build/en/guides/view-transitions/#full-site-view-transitions-spa-mode

## Google Analytics

Utilicé https://github.com/handystudio/astro-google-analytics#readme

Seguir el readme y como se dice allí, crear `.env` con `GA_ID=G-xxxxxx` que dice en:

<p align="center">
    <img src="docs/google%20analytics.png" />
</p>

Link de GA: https://analytics.google.com/analytics/web/#/p491398868

Para que no se contamine con mi development, setié ese valor en la env var de Netlify pero borré el archivo `.env` de este directorio.

Link al realtime overview:<br />
https://analytics.google.com/analytics/web/#/p491398868/realtime/overview?params=_u..nav%3Dmaui

## Traducciones

Probé https://github.com/yassinedoghri/astro-i18next pero no funca al deployar en Netlify

