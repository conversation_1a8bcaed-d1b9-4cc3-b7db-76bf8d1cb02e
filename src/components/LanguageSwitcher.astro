---
import { getLocaleFromUrl, getAlternateLocale, getLocalizedPath, removeLocaleFromPath, getLocaleDisplayName } from '../i18n/utils';

const currentLocale = getLocaleFromUrl(Astro.url);
const alternateLocale = getAlternateLocale(currentLocale);
const currentPath = removeLocaleFromPath(Astro.url.pathname);
const alternatePath = getLocalizedPath(currentPath, alternateLocale);
---

<div class="language-switcher">
  <a href={alternatePath} class="language-link">
    {getLocaleDisplayName(alternateLocale)}
  </a>
</div>

<style>
  .language-switcher {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
  }
  
  .language-link {
    display: inline-block;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 6px;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
  }
  
  .language-link:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #3245ff;
    color: #3245ff;
  }
</style>
