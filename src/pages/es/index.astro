---
import Welcome from '../../components/Welcome.astro';
import Layout from '../../layouts/Layout.astro';
import { getLocaleFromUrl, useTranslations, getLocalizedPath } from "../../i18n/utils";

const currentLocale = getLocaleFromUrl(Astro.url);
const t = useTranslations(currentLocale);
---

<Layout>
  <a href={getLocalizedPath('/holis', currentLocale)} style="position: fixed; top: 80px; right: 20px; z-index: 1000; padding: 8px 16px; background: rgba(255, 255, 255, 0.9); border: 1px solid #ddd; border-radius: 6px; text-decoration: none; color: #333;">
    {t('index.go_to_holis')}
  </a>
	<Welcome />
</Layout>
