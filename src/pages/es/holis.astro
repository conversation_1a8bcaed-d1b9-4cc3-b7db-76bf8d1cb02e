---
import Layout from '../../layouts/Layout.astro';
import { getLocaleFromUrl, useTranslations, getLocalizedPath } from "../../i18n/utils";

const currentLocale = getLocaleFromUrl(Astro.url);
const t = useTranslations(currentLocale);
---
<Layout>
  <div style="padding: 2rem; text-align: center;">
    <h1>{t('hello.title')}</h1>
    <a href={getLocalizedPath('/', currentLocale)} style="display: inline-block; margin-top: 1rem; padding: 8px 16px; background: #3245ff; color: white; text-decoration: none; border-radius: 6px;">
      {t('hello.go_to_home')}
    </a>
  </div>
</Layout>
